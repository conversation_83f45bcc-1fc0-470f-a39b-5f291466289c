# 省市区联动选择器实现

## 功能概述

已成功集成 `@react-native-picker/picker` 库，实现了省市区三级联动选择器功能。

## 主要特性

1. **三级联动选择**：省份、城市、区县三级联动选择
2. **数据源**：使用 `pca.json` 文件作为地区数据源
3. **用户体验**：
   - 选择省份后自动重置城市和区县
   - 选择城市后自动重置区县
   - 未选择上级地区时，下级选择器禁用
   - 模态框形式展示，支持取消和确认操作

## 实现细节

### 状态管理
```typescript
// 临时选择状态（用于模态框中的选择）
const [tempProvince, setTempProvince] = useState('');
const [tempCity, setTempCity] = useState('');
const [tempDistrict, setTempDistrict] = useState('');

// 模态框显示状态
const [showRegionPicker, setShowRegionPicker] = useState(false);
```

### 核心函数

1. **handleRegionSelect()**: 打开地区选择器
2. **handleProvinceChange()**: 处理省份选择，联动重置城市和区县
3. **handleCityChange()**: 处理城市选择，联动重置区县
4. **handleDistrictChange()**: 处理区县选择
5. **handleConfirmRegionSelect()**: 确认选择并更新表单数据
6. **handleCancelRegionSelect()**: 取消选择

### 数据结构

pca.json 数据结构：
```json
{
  "省份名": {
    "城市名": ["区县1", "区县2", "区县3"]
  }
}
```

### 样式特点

- 三列并排显示的选择器布局
- 响应式设计，适配不同屏幕尺寸
- 统一的主题色彩方案
- 清晰的标签和按钮设计

## 使用方法

1. 点击"省市区"输入框
2. 在弹出的模态框中依次选择省份、城市、区县
3. 点击"确定"按钮确认选择
4. 点击"取消"按钮放弃选择

## 验证逻辑

- 必须选择完整的省市区信息才能确认
- 如果信息不完整，会显示提示信息
- 选择确认后，表单数据会自动更新

## 依赖

- `@react-native-picker/picker`: React Native 选择器组件
- `pca.json`: 中国省市区数据文件
