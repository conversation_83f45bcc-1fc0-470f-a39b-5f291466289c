import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
  ScrollView,
  Modal,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Picker } from '@react-native-picker/picker';
import { ThemeContext } from '../../contextV2/themeContext';
import { PrizeInfoFormData } from './types';
import { getStyles } from './styles';
import { RootStackParamList } from '../../router/type';
import { StackScreenProps } from '@react-navigation/stack';
import BackBtn from '../../componentsV2/common/BackBtn';

export default function PrizeInfoForm(props: StackScreenProps<RootStackParamList>) {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const theme = useContext(ThemeContext);
  const styles = getStyles(theme);
    // 获取路由参数
  const { awardCode } = props.route.params as {
    awardCode: string;
    productName: string;
    coverPic: string;
  };
  

  const paddingTop = 10 + insets.top;

  const [formData, setFormData] = useState<PrizeInfoFormData>({
    realName: '',
    phoneNumber: '',
    province: '',
    city: '',
    district: '',
    detailAddress: '',
  });

  // 地区选择器相关状态
  const [showRegionPicker, setShowRegionPicker] = useState(false);
  const [tempProvince, setTempProvince] = useState('');
  const [tempCity, setTempCity] = useState('');
  const [tempDistrict, setTempDistrict] = useState('');

  // 解析 pca 数据
  const pcaData: Record<string, Record<string, string[]>> = require('./pca.json');
  const provinces = Object.keys(pcaData);
  const cities = tempProvince ? Object.keys(pcaData[tempProvince] || {}) : [];
  const districts: string[] = tempProvince && tempCity ? (pcaData[tempProvince]?.[tempCity] || []) : [];

  const handleSubmit = () => {
    // 验证表单
    if (!formData.realName.trim()) {
      Alert.alert('提示', '请输入真实姓名');
      return;
    }

    if (!formData.phoneNumber.trim()) {
      Alert.alert('提示', '请输入手机号');
      return;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.phoneNumber)) {
      Alert.alert('提示', '请输入正确的手机号');
      return;
    }

    if (!formData.province || !formData.city || !formData.district) {
      Alert.alert('提示', '请选择省市区');
      return;
    }

    if (!formData.detailAddress.trim()) {
      Alert.alert('提示', '请输入详细地址');
      return;
    }

    // TODO: 调用提交接口
    Alert.alert('提交成功', '您的信息已提交，我们会尽快处理', [
      {
        text: '确定',
        onPress: () => navigation.goBack(),
      },
    ]);
  };

  const handleRegionSelect = () => {
    // 初始化临时选择状态为当前表单数据
    setTempProvince(formData.province);
    setTempCity(formData.city);
    setTempDistrict(formData.district);
    setShowRegionPicker(true);
  };

  // 处理省份选择 - 联动重置城市和区县
  const handleProvinceChange = (province: string) => {
    setTempProvince(province);
    setTempCity(''); // 重置城市
    setTempDistrict(''); // 重置区县
  };

  // 处理城市选择 - 联动重置区县
  const handleCityChange = (city: string) => {
    setTempCity(city);
    setTempDistrict(''); // 重置区县
  };

  // 处理区县选择
  const handleDistrictChange = (district: string) => {
    setTempDistrict(district);
  };

  // 确认选择
  const handleConfirmRegionSelect = () => {
    if (tempProvince && tempCity && tempDistrict) {
      setFormData(prev => ({
        ...prev,
        province: tempProvince,
        city: tempCity,
        district: tempDistrict,
      }));
      setShowRegionPicker(false);
    } else {
      Alert.alert('提示', '请选择完整的省市区信息');
    }
  };

  // 取消选择
  const handleCancelRegionSelect = () => {
    setShowRegionPicker(false);
  };

  const updateFormData = (field: keyof PrizeInfoFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const getRegionText = () => {
    if (formData.province && formData.city && formData.district) {
      return `${formData.province} ${formData.city} ${formData.district}`;
    }
    return '';
  };

  return (
    <View style={styles.container}>
      {/* 头部 */}
      <View style={[styles.header, { paddingTop }]}>
        <View style={[styles.backBtn, { top: paddingTop }]}>
          <BackBtn onPress={navigation.goBack} />
        </View>
        <Text style={styles.title}> </Text>
      </View>

      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* 页面标题 */}
          <View>
            <Text style={styles.pageTitleText}>请填写中奖信息</Text>
          </View>

          {/* 兑换码 */}
          <View style={styles.formItemWithBorder}>
            <Text style={styles.label}>兑换码</Text>
            <Text style={styles.codeValue}>{awardCode}</Text>
          </View>

          {/* 真实姓名 */}
          <View style={styles.formItem}>
            <Text style={styles.label}>真实姓名</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.textInput}
                placeholder="请输入"
                placeholderTextColor={theme.prizeInfoForm.placeholder_text_color}
                value={formData.realName}
                onChangeText={(text) => updateFormData('realName', text)}
                maxLength={20}
              />
            </View>
          </View>

          {/* 手机号 */}
          <View style={styles.formItem}>
            <Text style={styles.label}>手机号</Text>
            <View style={styles.phoneInputContainer}>
              <Text style={styles.phonePrefix}>+86</Text>
              <View style={styles.phoneSeparator} />
              <TextInput
                style={styles.textInput}
                placeholder="请输入"
                placeholderTextColor={theme.prizeInfoForm.placeholder_text_color}
                value={formData.phoneNumber}
                onChangeText={(text) => updateFormData('phoneNumber', text)}
                keyboardType="numeric"
                maxLength={11}
              />
            </View>
          </View>

          {/* 省市区 */}
          <View style={styles.formItem}>
            <Text style={styles.label}>省市区</Text>
            <TouchableOpacity
              style={styles.regionSelector}
              onPress={handleRegionSelect}
              activeOpacity={1}
            >
              <Text style={styles.regionText}>
                {getRegionText() || '请选择'}
              </Text>
              <Image source={theme.prizeInfoForm.arrow_icon} style={styles.arrowIcon} />
            </TouchableOpacity>
          </View>

          {/* 详细地址 */}
          <View style={styles.formItem}>
            <Text style={styles.label}>详细地址</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.textInput}
                placeholder="请输入"
                placeholderTextColor={theme.prizeInfoForm.placeholder_text_color}
                value={formData.detailAddress}
                onChangeText={(text) => updateFormData('detailAddress', text)}
                multiline
                textAlignVertical="top"
              />
            </View>
          </View>
        </View>

        {/* 警告提示 */}
        <View style={styles.warning}>
          <Text style={styles.warningText}>
            信息提交后将无法修改，请您在提交前确认各项信息准确无误
          </Text>
        </View>

        {/* 提交按钮 */}
        <TouchableOpacity
          style={styles.submitButton}
          onPress={handleSubmit}
          activeOpacity={1}
        >
          <Text style={styles.submitButtonText}>提交</Text>
        </TouchableOpacity>
      </ScrollView>

      {/* 地区选择器 Modal */}
      <Modal
        visible={showRegionPicker}
        transparent={true}
        animationType="fade"
        onRequestClose={handleCancelRegionSelect}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {/* 头部 */}
            <View style={styles.modalHeader}>
              <TouchableOpacity onPress={handleCancelRegionSelect} style={styles.modalBackBtn}>
                <Text style={styles.modalBackText}>取消</Text>
              </TouchableOpacity>
              <Text style={styles.modalTitle}>选择地区</Text>
              <TouchableOpacity onPress={handleConfirmRegionSelect} style={styles.modalCloseBtn}>
                <Text style={styles.modalConfirmText}>确定</Text>
              </TouchableOpacity>
            </View>

            {/* 三列联动选择器 */}
            <View style={styles.pickerContainer}>
              <View style={styles.pickerRow}>
                {/* 省份选择器 */}
                <View style={styles.pickerColumn}>
                  <Text style={styles.pickerLabel}>省份</Text>
                  <Picker
                    selectedValue={tempProvince}
                    onValueChange={handleProvinceChange}
                    style={styles.picker}
                  >
                    <Picker.Item label="请选择" value="" />
                    {provinces.map((province) => (
                      <Picker.Item key={province} label={province} value={province} />
                    ))}
                  </Picker>
                </View>

                {/* 城市选择器 */}
                <View style={styles.pickerColumn}>
                  <Text style={styles.pickerLabel}>城市</Text>
                  <Picker
                    selectedValue={tempCity}
                    onValueChange={handleCityChange}
                    style={styles.picker}
                    enabled={!!tempProvince}
                  >
                    <Picker.Item label="请选择" value="" />
                    {cities.map((city) => (
                      <Picker.Item key={city} label={city} value={city} />
                    ))}
                  </Picker>
                </View>

                {/* 区县选择器 */}
                <View style={styles.pickerColumn}>
                  <Text style={styles.pickerLabel}>区县</Text>
                  <Picker
                    selectedValue={tempDistrict}
                    onValueChange={handleDistrictChange}
                    style={styles.picker}
                    enabled={!!tempCity}
                  >
                    <Picker.Item label="请选择" value="" />
                    {districts.map((district) => (
                      <Picker.Item key={district} label={district} value={district} />
                    ))}
                  </Picker>
                </View>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}