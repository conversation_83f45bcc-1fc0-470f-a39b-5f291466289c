import { StyleSheet } from 'react-native';
import { px } from '../../utils/px';
import { ThemeStyle } from '../../typesV2/themeInfo';


export const getStyles = (theme: ThemeStyle) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.container.bg_color,
  },
  header: {
    paddingTop: px(20),
    paddingBottom: px(10),
    zIndex: 100,
    flexDirection: 'row',
    justifyContent: 'center',
    backgroundColor: theme.common.bg_color,
  },
  backBtn: {
    position: 'absolute',
    left: 16,
  },
  title: {
    fontSize: px(17),
    lineHeight: px(24),
    color: theme.common.title_color,
  },
  content: {
    paddingTop: px(25),
    paddingHorizontal: px(16),
  },
  pageTitleText: {
    fontSize: px(20),
    fontWeight: '500',
    color: theme.prizeInfoForm.page_title_color,
  },
  formItemWithBorder: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingVertical: px(19),
    borderBottomWidth: px(0.5),
    borderBottomColor: theme.prizeInfoForm.form_border_color,
  },
  formItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: px(19),
    borderBottomWidth: px(0.5),
    borderBottomColor: theme.prizeInfoForm.form_border_color,
  },
  label: {
    fontSize: px(14),
    fontWeight: '600',
    color: theme.prizeInfoForm.label_color,
    minWidth: px(80),
  },
  codeValue: {
    fontSize: px(14),
    color: theme.prizeInfoForm.label_color,
    marginLeft: px(30),
  },
  inputContainer: {
    flex: 1,
    marginLeft: px(30),
  },
  phoneInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: px(30),
  },
  phonePrefix: {
    fontSize: px(16),
    color: theme.prizeInfoForm.label_color,
    marginRight: px(30),
  },
  phoneSeparator: {
    width: 1,
    height: px(16),
    backgroundColor: theme.prizeInfoForm.separator_color,
    marginRight: px(8),
  },
  textInput: {
    flex: 1,
    fontSize: px(16),
    color: theme.prizeInfoForm.text_input_color,
    padding: 0,
    height: px(22),
    textAlign: 'left',
  },
  regionSelector: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginLeft: px(20),
  },
  regionText: {
    fontSize: px(14),
    color: theme.prizeInfoForm.label_color,
    marginRight: px(4),
  },
  arrowIcon: {
    width: px(11),
    height: px(20),
    resizeMode: 'contain',
  },
  warning: {
    marginHorizontal: px(25.5),
    marginTop: px(35),
    marginBottom: px(12),
  },
  warningText: {
    fontSize: px(12),
    color: '#FF4444',
    textAlign: 'center',
  },
  submitButton: {
    backgroundColor: 'rgba(255, 68, 68, 0.3)',
    height: px(44),
    borderRadius: px(22),
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: px(16),
    marginTop: px(12),
  },
  submitButtonText: {
    fontSize: px(14),
    fontWeight: '600',
    color: '#FFFFFF',
  },
  // 地区选择器样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: px(20),
  },
  modalContent: {
    backgroundColor: theme.common.bg_color,
    borderRadius: px(12),
    width: '100%',
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: px(16),
    paddingVertical: px(16),
    borderBottomWidth: px(0.5),
    borderBottomColor: theme.prizeInfoForm.form_border_color,
  },
  modalBackBtn: {
    paddingHorizontal: px(8),
    paddingVertical: px(4),
  },
  modalBackText: {
    fontSize: px(16),
    color: theme.prizeInfoForm.label_color,
  },
  modalTitle: {
    fontSize: px(18),
    fontWeight: '600',
    color: theme.prizeInfoForm.label_color,
    flex: 1,
    textAlign: 'center',
  },
  modalCloseBtn: {
    paddingHorizontal: px(8),
    paddingVertical: px(4),
  },
  modalCloseText: {
    fontSize: px(16),
    color: theme.prizeInfoForm.label_color,
  },
  modalConfirmText: {
    fontSize: px(16),
    color: '#FF4444',
    fontWeight: '600',
  },
  pickerContainer: {
    paddingHorizontal: px(16),
    paddingBottom: px(20),
  },
  pickerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  pickerColumn: {
    flex: 1,
    marginHorizontal: px(4),
  },
  pickerLabel: {
    fontSize: px(14),
    fontWeight: '600',
    color: theme.prizeInfoForm.label_color,
    textAlign: 'center',
    marginBottom: px(8),
  },
  picker: {
    height: px(180),
    color: theme.prizeInfoForm.text_input_color,
  },
});