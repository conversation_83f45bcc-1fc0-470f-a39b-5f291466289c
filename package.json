{"name": "rn_credit_center", "version": "0.0.1", "private": true, "main": "index.tsx", "author": {"name": "luogao"}, "scripts": {"start": "node devQR && react-native start --reset-cache ", "start:debug": "NODE_OPTIONS='--inspect' DEBUG=Metro:Server react-native start --reset-cache --max-workers 1", "pack:android:test": "react-native ram-bundle --entry-file index.tsx --platform android --dev false --bundle-name rn_credit_center --bundle-version android_test_0.0.1 --minify true --env 1", "pack:android:prod": "react-native ram-bundle --entry-file index.tsx --platform android --dev false --bundle-name rn_credit_center --bundle-version android_prod_0.0.1 --minify true --env 0", "pack:ios:test": "react-native ram-bundle --entry-file index.tsx --platform ios --dev false --bundle-name rn_credit_center --bundle-version ios_test_0.0.1 --minify true --env 1", "pack:ios:prod": "react-native ram-bundle --entry-file index.tsx --platform ios --dev false --bundle-name rn_credit_center --bundle-version ios_prod_0.0.1 --minify true --env 0", "lint": "node_modules/eslint/bin/eslint.js src --max-warnings 0", "image": "node imageProcessor.js", "patchViewPager": "node viewPagerPatch.js", "test:pack": "react-native ram-bundle --entry-file index.tsx --bundle-version android-iqjyMm99oO --bundle-name rn_credit_center --reset-cache  --platform android --minify true --bundle-dir bundles --dev false", "postinstall": "patch-package"}, "dependencies": {"@react-native-community/async-storage": "1.12.0", "@react-native-community/blur": "3.3.1", "@react-native-community/cameraroll": "4.0.0", "@react-native-community/clipboard": "1.2.3", "@react-native-community/masked-view": "0.1.10", "@react-native-picker/picker": "^2.11.1", "@react-navigation/native": "5.7.3", "@react-navigation/stack": "5.9.0", "@rematch/core": "^2.0.0-next.10", "@xmly/mamba-console": "0.0.12", "@xmly/morty": "file:.yalc/@xmly/morty", "@xmly/react-native-page-analytics": "0.4.26", "@xmly/react-native-shadow-2": "4.0.0", "@xmly/react-native-svg": "12.2.1", "@xmly/react-native-wheel-picker": "1.0.8", "@xmly/rn-components": "^1.0.23", "@xmly/rn-movable": "^0.0.9", "@xmly/rn-sdk": "0.0.29", "@xmly/rn-utils": "0.0.26", "@xmly/sentry-react-native": "3.0.2", "@xmly/xmlog-rn": "1.0.2", "base-64": "^1.0.0", "crypto-js": "^4.1.1", "dayjs": "^1.9.6", "jotai": "^2.9.3", "lodash.chunk": "^4.2.0", "lodash.get": "^4.4.2", "lodash.isequal": "^4.5.0", "lodash.last": "^3.0.0", "lodash.take": "^4.1.1", "lottie-react-native": "4.0.0", "query-string": "^7.0.1", "react": "16.13.1", "react-native": "git+http://gitlab.ximalaya.com/react-native-app/xm-react-native-0.56.1.git#0.63-hermes-supportCompile", "react-native-fs": "^2.18.0", "react-native-gesture-handler": "1.7.0", "react-native-haptic": "1.0.1", "react-native-linear-gradient": "2.5.6", "react-native-permissions": "2.2.0", "react-native-reanimated": "1.12.0", "react-native-redash": "9.6.0", "react-native-safe-area-context": "3.1.7", "react-native-screens": "2.10.1", "react-native-snap-carousel": "^3.9.1", "react-native-tab-view": "2.16.0", "react-native-text-ticker": "^1.14.0", "react-native-webview": "10.8.2", "react-navigation-lazy-screen": "1.1.5", "react-redux": "7.2.1", "redux": "4.0.5", "styled-components": "3.4.10"}, "devDependencies": {"@babel/core": "7.11.6", "@babel/plugin-proposal-decorators": "7.0.0-beta.47", "@babel/runtime": "7.11.2", "@carimus/metro-symlinked-deps": "^1.1.0", "@react-native-community/eslint-config": "^2.0.0", "@types/base-64": "^1.0.0", "@types/crypto-js": "^4.1.2", "@types/lodash.chunk": "^4.2.6", "@types/lodash.get": "^4.4.6", "@types/lodash.isequal": "^4.5.5", "@types/lodash.last": "^3.0.6", "@types/lodash.take": "^4.1.6", "@types/react-native": "0.63", "@types/react-native-snap-carousel": "^3.8.2", "@types/react-redux": "7.1.9", "@xmly/react-native-launch-performance": "^0.1.9", "babel-eslint": "8.2.6", "babel-jest": "25.5.1", "babel-plugin-import": "^1.13.3", "babel-plugin-transform-remove-console": "6.9.4", "babel-plugin-transform-slow-func-detecter": "^2.1.0", "babel-plugin-transform-strip-block": "^0.0.4", "eslint": "^7.18.0", "eslint-plugin-prettier": "^3.3.1", "hermes-engine": "0.6.0", "jest": "25.5.4", "metro-react-native-babel-preset": "0.59.0", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "^2.2.1", "qrcode-terminal": "^0.12.0", "react-native-typescript-transformer": "1.2.13", "react-test-renderer": "16.13.1", "standard": "14.3.4", "typescript": "4.4.2"}, "resolutions": {"@types/react": "^18.0.8", "react-native": "git+http://gitlab.ximalaya.com/react-native-app/xm-react-native-0.56.1.git#0.63-hermes-supportCompile"}, "standard": {"parser": "babel-es<PERSON>"}, "jest": {"preset": "react-native"}, "mambaId": 123}